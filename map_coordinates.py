import cv2
import numpy as np
import yaml

def click_event(event, x, y, flags, param):
    if event == cv2.EVENT_LBUTTONDOWN:  # 当鼠标左键点击时
        # 获取配置参数
        config = param['config']
        resolution = config['resolution']
        origin = config['origin']
        
        # 将像素坐标转换为实际坐标
        # 注意：图片坐标系的y轴是向下的，而实际坐标系的y轴是向上的
        real_x = origin[0] + (x * resolution)
        real_y = origin[1] + ((param['height'] - y) * resolution)  # 翻转y轴
        
        print(f"像素坐标: ({x}, {y})")
        print(f"实际坐标: ({real_x:.6f}, {real_y:.6f})")
        
        # 在图片上标记点击位置
        cv2.circle(param['image'], (x, y), 3, (0, 0, 255), -1)
        cv2.imshow('Map', param['image'])

def main():
    # 读取配置文件
    with open('911.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # 读取图片
    image = cv2.imread('911.png')
    if image is None:
        print("Error: Cannot load map image")
        return
    
    height, width = image.shape[:2]
    
    # 设置缩放参数
    scale_factor = 0.8  # 可以调整这个值来改变显示大小
    display_width = int(width * scale_factor)
    display_height = int(height * scale_factor)

    # 缩放图像用于显示
    display_image = cv2.resize(image, (display_width, display_height))

    # 创建窗口并设置鼠标回调
    cv2.imshow('Map', display_image)
    param = {
        'image': image,
        'config': config,
        'height': height
    }
    cv2.setMouseCallback('Map', click_event, param)
    
    print("点击地图上的任意位置获取坐标。按'q'键退出。")
    
    # 等待按键
    while True:
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
    
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
